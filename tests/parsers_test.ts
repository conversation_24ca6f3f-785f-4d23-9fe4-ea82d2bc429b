import { assertEquals, assertExists } from "@std/assert/mod.ts";
import { VmessParser } from "../src/parsers/vmess.ts";
import { VlessParser } from "../src/parsers/vless.ts";
import { TrojanParser } from "../src/parsers/trojan.ts";
import { ShadowsocksParser } from "../src/parsers/shadowsocks.ts";
import { SubscriptionParser } from "../src/parsers/subscription.ts";

Deno.test("VMess解析器测试", () => {
  const parser = new VmessParser();
  
  // 测试有效的VMess链接
  const vmessUrl = "vmess://" + btoa(JSON.stringify({
    v: "2",
    ps: "测试节点",
    add: "example.com",
    port: "443",
    id: "12345678-1234-1234-1234-123456789abc",
    aid: "0",
    scy: "auto",
    net: "tcp",
    type: "none",
    host: "",
    path: "",
    tls: "tls"
  }));

  const node = parser.parse(vmessUrl);
  assertExists(node);
  assertEquals(node?.name, "测试节点");
  assertEquals(node?.server, "example.com");
  assertEquals(node?.port, 443);
  assertEquals(node?.type, "vmess");
  assertEquals(node?.tls, true);
});

Deno.test("VLESS解析器测试", () => {
  const parser = new VlessParser();
  
  // 测试有效的VLESS链接
  const vlessUrl = "vless://<EMAIL>:443?type=tcp&security=tls&sni=example.com#测试节点";

  const node = parser.parse(vlessUrl);
  assertExists(node);
  assertEquals(node?.name, "测试节点");
  assertEquals(node?.server, "example.com");
  assertEquals(node?.port, 443);
  assertEquals(node?.type, "vless");
  assertEquals(node?.tls, true);
  assertEquals(node?.sni, "example.com");
});

Deno.test("Trojan解析器测试", () => {
  const parser = new TrojanParser();
  
  // 测试有效的Trojan链接
  const trojanUrl = "trojan://<EMAIL>:443?sni=example.com#测试节点";

  const node = parser.parse(trojanUrl);
  assertExists(node);
  assertEquals(node?.name, "测试节点");
  assertEquals(node?.server, "example.com");
  assertEquals(node?.port, 443);
  assertEquals(node?.type, "trojan");
  assertEquals(node?.password, "password123");
  assertEquals(node?.tls, true);
});

Deno.test("Shadowsocks解析器测试", () => {
  const parser = new ShadowsocksParser();
  
  // 测试有效的SS链接
  const ssUrl = "ss://" + btoa("aes-256-gcm:password123") + "@example.com:8388#测试节点";

  const node = parser.parse(ssUrl);
  assertExists(node);
  assertEquals(node?.name, "测试节点");
  assertEquals(node?.server, "example.com");
  assertEquals(node?.port, 8388);
  assertEquals(node?.type, "ss");
  assertEquals(node?.cipher, "aes-256-gcm");
  assertEquals(node?.password, "password123");
});

Deno.test("订阅解析器测试", () => {
  const parser = new SubscriptionParser();
  
  // 测试Base64编码的订阅内容
  const vmessUrl = "vmess://" + btoa(JSON.stringify({
    v: "2",
    ps: "节点1",
    add: "server1.com",
    port: "443",
    id: "12345678-1234-1234-1234-123456789abc",
    aid: "0",
    scy: "auto",
    net: "tcp",
    type: "none",
    tls: "tls"
  }));

  const vlessUrl = "vless://<EMAIL>:443?type=tcp&security=tls#节点2";
  
  const subscriptionContent = btoa([vmessUrl, vlessUrl].join("\n"));
  
  const nodes = parser.parseSubscriptionContent(subscriptionContent);
  assertEquals(nodes.length, 2);
  assertEquals(nodes[0].name, "节点1");
  assertEquals(nodes[1].name, "节点2");
});

Deno.test("无效链接处理测试", () => {
  const vmessParser = new VmessParser();
  const vlessParser = new VlessParser();
  const trojanParser = new TrojanParser();
  const ssParser = new ShadowsocksParser();

  // 测试无效链接
  assertEquals(vmessParser.parse("invalid://url"), null);
  assertEquals(vlessParser.parse("invalid://url"), null);
  assertEquals(trojanParser.parse("invalid://url"), null);
  assertEquals(ssParser.parse("invalid://url"), null);

  // 测试格式错误的链接
  assertEquals(vmessParser.parse("vmess://invalid-base64"), null);
  assertEquals(vlessParser.parse("vless://missing-parts"), null);
});
