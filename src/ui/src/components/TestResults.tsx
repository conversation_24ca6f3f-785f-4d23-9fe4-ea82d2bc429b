import React, { useState, useEffect } from 'react';
import { ApiService } from '../services/api';

interface ProxyNode {
  id: string;
  name: string;
  type: string;
  server: string;
  port: number;
  testResult?: {
    connectivity: boolean;
    latency: number;
    downloadSpeed: number;
    uploadSpeed: number;
    timestamp: number;
    error?: string;
  };
}

interface TestResultsProps {
  nodes: ProxyNode[];
}

export const TestResults: React.FC<TestResultsProps> = ({ nodes }) => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [sortBy, setSortBy] = useState<'latency' | 'downloadSpeed' | 'uploadSpeed' | 'timestamp'>('latency');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filter, setFilter] = useState({
    connectivity: '',
    protocol: '',
  });

  // 加载测试结果
  const loadTestResults = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getTestResults();
      if (response.success) {
        setTestResults(response.data || []);
      }
    } catch (error) {
      console.error('加载测试结果失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTestResults();
  }, []);

  // 合并节点信息和测试结果
  const mergedResults = testResults.map(result => {
    const node = nodes.find(n => n.id === result.nodeId);
    return {
      ...result,
      node,
    };
  }).filter(result => result.node); // 只显示有节点信息的结果

  // 过滤结果
  const filteredResults = mergedResults.filter(result => {
    if (filter.connectivity === 'connected' && !result.connectivity) return false;
    if (filter.connectivity === 'disconnected' && result.connectivity) return false;
    if (filter.protocol && result.node.type !== filter.protocol) return false;
    return true;
  });

  // 排序结果
  const sortedResults = [...filteredResults].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // 处理特殊值
    if (sortBy !== 'timestamp') {
      if (aValue < 0) aValue = Infinity;
      if (bValue < 0) bValue = Infinity;
    }

    if (sortOrder === 'asc') {
      return aValue - bValue;
    } else {
      return bValue - aValue;
    }
  });

  // 计算统计信息
  const stats = {
    total: filteredResults.length,
    connected: filteredResults.filter(r => r.connectivity).length,
    avgLatency: 0,
    avgDownloadSpeed: 0,
    avgUploadSpeed: 0,
  };

  const connectedResults = filteredResults.filter(r => r.connectivity && r.latency > 0);
  if (connectedResults.length > 0) {
    stats.avgLatency = Math.round(
      connectedResults.reduce((sum, r) => sum + r.latency, 0) / connectedResults.length
    );
  }

  const speedResults = filteredResults.filter(r => r.connectivity && r.downloadSpeed > 0);
  if (speedResults.length > 0) {
    stats.avgDownloadSpeed = Math.round(
      speedResults.reduce((sum, r) => sum + r.downloadSpeed, 0) / speedResults.length
    );
    stats.avgUploadSpeed = Math.round(
      speedResults.reduce((sum, r) => sum + r.uploadSpeed, 0) / speedResults.length
    );
  }

  // 格式化函数
  const formatLatency = (latency: number) => {
    if (latency < 0) return '超时';
    return `${latency}ms`;
  };

  const formatSpeed = (speed: number) => {
    if (speed < 0) return '未测试';
    if (speed < 1024) return `${speed}KB/s`;
    return `${(speed / 1024).toFixed(1)}MB/s`;
  };

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  return (
    <div className="test-results">
      <div className="section">
        <h2>🔍 测试结果</h2>

        {/* 统计信息 */}
        <div className="card">
          <h3>统计信息</h3>
          <div className="stats">
            <div className="stat-item">
              <span className="stat-label">总节点数:</span>
              <span className="stat-value">{stats.total}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">可连通:</span>
              <span className="stat-value connected">{stats.connected}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">连通率:</span>
              <span className="stat-value">
                {stats.total > 0 ? Math.round((stats.connected / stats.total) * 100) : 0}%
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">平均延迟:</span>
              <span className="stat-value">{formatLatency(stats.avgLatency)}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">平均下载速度:</span>
              <span className="stat-value">{formatSpeed(stats.avgDownloadSpeed)}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">平均上传速度:</span>
              <span className="stat-value">{formatSpeed(stats.avgUploadSpeed)}</span>
            </div>
          </div>
        </div>

        {/* 过滤器 */}
        <div className="card">
          <h3>筛选条件</h3>
          <div className="filters">
            <div className="filter-group">
              <label>连通性:</label>
              <select
                value={filter.connectivity}
                onChange={(e) => setFilter({ ...filter, connectivity: e.target.value })}
              >
                <option value="">全部</option>
                <option value="connected">已连通</option>
                <option value="disconnected">已断开</option>
              </select>
            </div>
            <div className="filter-group">
              <label>协议类型:</label>
              <select
                value={filter.protocol}
                onChange={(e) => setFilter({ ...filter, protocol: e.target.value })}
              >
                <option value="">全部</option>
                <option value="vmess">VMess</option>
                <option value="vless">VLESS</option>
                <option value="trojan">Trojan</option>
                <option value="ss">Shadowsocks</option>
              </select>
            </div>
          </div>
          <div className="filter-actions">
            <button onClick={loadTestResults} disabled={loading}>
              {loading ? '刷新中...' : '刷新结果'}
            </button>
          </div>
        </div>

        {/* 结果表格 */}
        <div className="card">
          <h3>测试结果详情</h3>
          {loading ? (
            <div className="loading">加载中...</div>
          ) : sortedResults.length === 0 ? (
            <div className="empty">没有测试结果</div>
          ) : (
            <div className="results-table">
              <table>
                <thead>
                  <tr>
                    <th>节点名称</th>
                    <th>类型</th>
                    <th>服务器</th>
                    <th>连通性</th>
                    <th 
                      className={`sortable ${sortBy === 'latency' ? sortOrder : ''}`}
                      onClick={() => handleSort('latency')}
                    >
                      延迟 {sortBy === 'latency' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className={`sortable ${sortBy === 'downloadSpeed' ? sortOrder : ''}`}
                      onClick={() => handleSort('downloadSpeed')}
                    >
                      下载速度 {sortBy === 'downloadSpeed' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className={`sortable ${sortBy === 'uploadSpeed' ? sortOrder : ''}`}
                      onClick={() => handleSort('uploadSpeed')}
                    >
                      上传速度 {sortBy === 'uploadSpeed' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </th>
                    <th 
                      className={`sortable ${sortBy === 'timestamp' ? sortOrder : ''}`}
                      onClick={() => handleSort('timestamp')}
                    >
                      测试时间 {sortBy === 'timestamp' && (sortOrder === 'asc' ? '↑' : '↓')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedResults.map((result) => (
                    <tr key={`${result.nodeId}-${result.timestamp}`}>
                      <td className="node-name" title={result.node.name}>
                        {result.node.name}
                      </td>
                      <td className={`node-type ${result.node.type}`}>
                        {result.node.type.toUpperCase()}
                      </td>
                      <td>{result.node.server}</td>
                      <td className={`connectivity ${result.connectivity ? 'connected' : 'disconnected'}`}>
                        {result.connectivity ? '连通' : '断开'}
                        {result.error && (
                          <span className="error-info" title={result.error}>⚠️</span>
                        )}
                      </td>
                      <td className={result.latency < 100 ? 'good' : result.latency < 300 ? 'medium' : 'poor'}>
                        {formatLatency(result.latency)}
                      </td>
                      <td className={result.downloadSpeed > 1024 ? 'good' : result.downloadSpeed > 512 ? 'medium' : 'poor'}>
                        {formatSpeed(result.downloadSpeed)}
                      </td>
                      <td className={result.uploadSpeed > 512 ? 'good' : result.uploadSpeed > 256 ? 'medium' : 'poor'}>
                        {formatSpeed(result.uploadSpeed)}
                      </td>
                      <td>{new Date(result.timestamp).toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
