import React, { useState } from 'react';
import { ApiService } from '../services/api';

interface ProxyNode {
  id: string;
  name: string;
  type: string;
  server: string;
  port: number;
  testResult?: {
    connectivity: boolean;
    latency: number;
    downloadSpeed: number;
    uploadSpeed: number;
    timestamp: number;
    error?: string;
  };
}

interface ConfigGeneratorProps {
  nodes: ProxyNode[];
  selectedNodes: string[];
}

export const ConfigGenerator: React.FC<ConfigGeneratorProps> = ({ nodes, selectedNodes }) => {
  const [config, setConfig] = useState({
    templatePath: 'templates/mihomo-template.yaml',
    outputPath: 'config.yaml',
    groupName: '🚀 节点选择',
    insertToGroups: true,
    backupOriginal: true,
    useSelectedOnly: true,
  });
  const [filter, setFilter] = useState({
    connectivity: 'connected',
    maxLatency: 500,
    minSpeed: 0,
    protocols: [] as string[],
  });
  const [generating, setGenerating] = useState(false);
  const [message, setMessage] = useState('');
  const [generatedConfig, setGeneratedConfig] = useState<any>(null);

  // 获取要使用的节点
  const getFilteredNodes = () => {
    let nodesToUse = config.useSelectedOnly && selectedNodes.length > 0
      ? nodes.filter(node => selectedNodes.includes(node.id))
      : nodes;

    // 应用过滤条件
    return nodesToUse.filter(node => {
      // 连通性过滤
      if (filter.connectivity === 'connected' && (!node.testResult || !node.testResult.connectivity)) {
        return false;
      }
      if (filter.connectivity === 'disconnected' && node.testResult?.connectivity) {
        return false;
      }

      // 延迟过滤
      if (filter.maxLatency > 0 && node.testResult && node.testResult.latency > filter.maxLatency) {
        return false;
      }

      // 速度过滤
      if (filter.minSpeed > 0 && node.testResult && node.testResult.downloadSpeed < filter.minSpeed) {
        return false;
      }

      // 协议过滤
      if (filter.protocols.length > 0 && !filter.protocols.includes(node.type)) {
        return false;
      }

      return true;
    });
  };

  const filteredNodes = getFilteredNodes();

  // 生成配置文件
  const handleGenerateConfig = async () => {
    if (filteredNodes.length === 0) {
      setMessage('没有符合条件的节点');
      return;
    }

    try {
      setGenerating(true);
      setMessage('正在生成配置文件...');

      const response = await ApiService.generateConfig({
        templatePath: config.templatePath,
        nodeIds: filteredNodes.map(node => node.id),
        outputPath: config.outputPath,
        options: {
          groupName: config.groupName,
          insertToGroups: config.insertToGroups,
          backupOriginal: config.backupOriginal,
        },
      });

      if (response.success) {
        setGeneratedConfig(response.data.config);
        setMessage(`配置文件生成成功，包含 ${filteredNodes.length} 个节点`);
      } else {
        setMessage(`生成失败: ${response.error}`);
      }
    } catch (error) {
      setMessage(`生成失败: ${error}`);
    } finally {
      setGenerating(false);
    }
  };

  // 下载配置文件
  const handleDownloadConfig = () => {
    if (!generatedConfig) return;

    const yamlContent = JSON.stringify(generatedConfig, null, 2); // 简化处理，实际应该转换为YAML
    const blob = new Blob([yamlContent], { type: 'text/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = config.outputPath;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 处理协议选择
  const handleProtocolToggle = (protocol: string) => {
    const newProtocols = filter.protocols.includes(protocol)
      ? filter.protocols.filter(p => p !== protocol)
      : [...filter.protocols, protocol];
    setFilter({ ...filter, protocols: newProtocols });
  };

  return (
    <div className="config-generator">
      <div className="section">
        <h2>⚙️ 配置生成</h2>

        {/* 基本配置 */}
        <div className="card">
          <h3>基本配置</h3>
          <div className="form-group">
            <label>模板文件路径:</label>
            <input
              type="text"
              value={config.templatePath}
              onChange={(e) => setConfig({ ...config, templatePath: e.target.value })}
              placeholder="模板文件路径"
            />
          </div>
          <div className="form-group">
            <label>输出文件路径:</label>
            <input
              type="text"
              value={config.outputPath}
              onChange={(e) => setConfig({ ...config, outputPath: e.target.value })}
              placeholder="输出文件路径"
            />
          </div>
          <div className="form-group">
            <label>代理组名称:</label>
            <input
              type="text"
              value={config.groupName}
              onChange={(e) => setConfig({ ...config, groupName: e.target.value })}
              placeholder="代理组名称"
            />
          </div>
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={config.insertToGroups}
                onChange={(e) => setConfig({ ...config, insertToGroups: e.target.checked })}
              />
              插入到代理组
            </label>
          </div>
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={config.backupOriginal}
                onChange={(e) => setConfig({ ...config, backupOriginal: e.target.checked })}
              />
              备份原始文件
            </label>
          </div>
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={config.useSelectedOnly}
                onChange={(e) => setConfig({ ...config, useSelectedOnly: e.target.checked })}
              />
              仅使用选中的节点 ({selectedNodes.length} 个)
            </label>
          </div>
        </div>

        {/* 节点过滤 */}
        <div className="card">
          <h3>节点过滤</h3>
          <div className="filter-group">
            <label>连通性:</label>
            <select
              value={filter.connectivity}
              onChange={(e) => setFilter({ ...filter, connectivity: e.target.value })}
            >
              <option value="">全部</option>
              <option value="connected">仅连通节点</option>
              <option value="disconnected">仅断开节点</option>
            </select>
          </div>
          <div className="filter-group">
            <label>最大延迟 (ms):</label>
            <input
              type="number"
              value={filter.maxLatency}
              onChange={(e) => setFilter({ ...filter, maxLatency: parseInt(e.target.value) || 0 })}
              placeholder="0 = 不限制"
              min="0"
            />
          </div>
          <div className="filter-group">
            <label>最小速度 (KB/s):</label>
            <input
              type="number"
              value={filter.minSpeed}
              onChange={(e) => setFilter({ ...filter, minSpeed: parseInt(e.target.value) || 0 })}
              placeholder="0 = 不限制"
              min="0"
            />
          </div>
          <div className="filter-group">
            <label>协议类型:</label>
            <div className="protocol-checkboxes">
              {['vmess', 'vless', 'trojan', 'ss'].map(protocol => (
                <label key={protocol}>
                  <input
                    type="checkbox"
                    checked={filter.protocols.includes(protocol)}
                    onChange={() => handleProtocolToggle(protocol)}
                  />
                  {protocol.toUpperCase()}
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* 节点预览 */}
        <div className="card">
          <h3>节点预览</h3>
          <div className="node-preview">
            <div className="preview-stats">
              <span>符合条件的节点: {filteredNodes.length} 个</span>
              {config.useSelectedOnly && selectedNodes.length > 0 && (
                <span>（从 {selectedNodes.length} 个选中节点中筛选）</span>
              )}
            </div>
            {filteredNodes.length > 0 && (
              <div className="preview-list">
                {filteredNodes.slice(0, 10).map(node => (
                  <div key={node.id} className="preview-item">
                    <span className={`node-type ${node.type}`}>{node.type.toUpperCase()}</span>
                    <span className="node-name">{node.name}</span>
                    {node.testResult && (
                      <span className={`connectivity ${node.testResult.connectivity ? 'connected' : 'disconnected'}`}>
                        {node.testResult.connectivity ? '✓' : '✗'}
                      </span>
                    )}
                  </div>
                ))}
                {filteredNodes.length > 10 && (
                  <div className="preview-more">还有 {filteredNodes.length - 10} 个节点...</div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 生成操作 */}
        <div className="card">
          <div className="generate-actions">
            <button 
              onClick={handleGenerateConfig} 
              disabled={generating || filteredNodes.length === 0}
              className="primary"
            >
              {generating ? '生成中...' : '生成配置文件'}
            </button>
            {generatedConfig && (
              <button onClick={handleDownloadConfig} className="secondary">
                下载配置文件
              </button>
            )}
          </div>
        </div>

        {/* 生成结果 */}
        {generatedConfig && (
          <div className="card">
            <h3>生成结果</h3>
            <div className="config-preview">
              <div className="config-info">
                <p>✅ 配置文件生成成功</p>
                <p>📊 包含 {filteredNodes.length} 个代理节点</p>
                <p>📁 输出路径: {config.outputPath}</p>
              </div>
              <details>
                <summary>查看配置预览</summary>
                <pre className="config-content">
                  {JSON.stringify(generatedConfig, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}

        {/* 消息提示 */}
        {message && (
          <div className={`message ${message.includes('失败') ? 'error' : 'success'}`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
};
